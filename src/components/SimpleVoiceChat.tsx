import React, { useEffect } from 'react';
import { useRealTimeConversation } from '../hooks/useRealTimeConversation';
import type { ConversationMessage } from '../hooks/useRealTimeConversation';

interface SimpleVoiceChatProps {
  generatedCharacter?: string;
  isGameStarted: boolean;
  initialMessage?: string; // 👈 Nuevo prop
}

const AnimatedVoiceButton: React.FC<{
  state: string;
  isActive: boolean;
  onClick: () => void;
}> = ({ state, isActive, onClick }) => {
  const getButtonConfig = () => {
    if (!isActive) {
      return {
        icon: '🎤',
        text: 'Iniciar Conversación',
        color: '#28a745',
        bgColor: '#d4edda',
        borderColor: '#c3e6cb'
      };
    }

    switch (state) {
      case 'listening':
        return {
          icon: '🎤',
          text: 'Escuchando...',
          color: '#28a745',
          bgColor: '#d4edda',
          borderColor: '#c3e6cb',
          pulse: true
        };
      case 'processing':
        return {
          icon: '⚙️',
          text: 'Procesando...',
          color: '#ffc107',
          bgColor: '#fff3cd',
          borderColor: '#ffeaa7',
          spin: true
        };
      case 'speaking':
        return {
          icon: '🔊',
          text: 'IA hablando...',
          color: '#17a2b8',
          bgColor: '#d1ecf1',
          borderColor: '#bee5eb',
          bounce: true
        };
      default:
        return {
          icon: '⭕',
          text: 'Activo',
          color: '#6c757d',
          bgColor: '#e2e3e5',
          borderColor: '#d6d8db'
        };
    }
  };

  const config = getButtonConfig();

  const animationStyle = {
    ...(config.pulse && {
      animation: 'pulse 1.5s ease-in-out infinite'
    }),
    ...(config.spin && {
      animation: 'spin 1s linear infinite'
    }),
    ...(config.bounce && {
      animation: 'bounce 0.6s ease-in-out infinite'
    })
  };

  return (
    <>
      <style>{`
        @keyframes pulse {
          0%, 100% { transform: scale(1); opacity: 1; }
          50% { transform: scale(1.05); opacity: 0.8; }
        }
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
        @keyframes bounce {
          0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
          40% { transform: translateY(-5px); }
          60% { transform: translateY(-3px); }
        }
      `}</style>

      <button
        onClick={onClick}
        style={{
          display: 'flex',
          alignItems: 'center',
          gap: '12px',
          padding: '16px 24px',
          backgroundColor: config.bgColor,
          color: config.color,
          border: `2px solid ${config.borderColor}`,
          borderRadius: '50px',
          cursor: 'pointer',
          fontSize: '16px',
          fontWeight: 'bold',
          boxShadow: '0 4px 12px rgba(0,0,0,0.1)',
          transition: 'all 0.3s ease',
          ...animationStyle
        }}
        onMouseOver={(e) => {
          e.currentTarget.style.transform = 'translateY(-2px)';
          e.currentTarget.style.boxShadow = '0 6px 20px rgba(0,0,0,0.15)';
        }}
        onMouseOut={(e) => {
          e.currentTarget.style.transform = 'translateY(0)';
          e.currentTarget.style.boxShadow = '0 4px 12px rgba(0,0,0,0.1)';
        }}
      >
        <span style={{ fontSize: '20px' }}>{config.icon}</span>
        <span>{config.text}</span>
      </button>
    </>
  );
};

const MessageBubble: React.FC<{ message: ConversationMessage }> = ({ message }) => {
  const isUser = message.type === 'user';

  return (
    <div style={{
      display: 'flex',
      justifyContent: isUser ? 'flex-end' : 'flex-start',
      marginBottom: '16px',
      animation: 'slideIn 0.3s ease-out'
    }}>
      <div style={{
        maxWidth: '85%',
        padding: '14px 18px',
        borderRadius: isUser ? '20px 20px 4px 20px' : '20px 20px 20px 4px',
        backgroundColor: isUser ? '#007bff' : '#f8f9fa',
        color: isUser ? 'white' : '#333',
        fontSize: '15px',
        lineHeight: '1.4',
        boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
        opacity: message.isInterim ? 0.7 : 1,
        border: message.isInterim ? '2px dashed #007bff' : 'none',
        position: 'relative'
      }}>
        <div style={{ marginBottom: message.isInterim ? '8px' : '4px' }}>
          {message.content}
        </div>

        {message.isInterim && (
          <div style={{
            fontSize: '11px',
            opacity: 0.8,
            fontStyle: 'italic',
            color: isUser ? '#e3f2fd' : '#666'
          }}>
            Escribiendo...
          </div>
        )}

        <div style={{
          fontSize: '11px',
          opacity: 0.7,
          marginTop: '6px',
          textAlign: 'right',
          color: isUser ? '#e3f2fd' : '#888'
        }}>
          {message.timestamp.toLocaleTimeString([], {
            hour: '2-digit',
            minute: '2-digit'
          })}
        </div>
      </div>
    </div>
  );
};

export const SimpleVoiceChat: React.FC<SimpleVoiceChatProps> = ({
  generatedCharacter,
  isGameStarted,
  initialMessage // 👈 Recibir mensaje inicial
}) => {
  const {
    isActive,
    conversationState,
    messages,
    isSupported,
    error,
    startConversation,
    stopConversation,
    enableSmartMicrophone,
    addInitialMessage // 👈 Función para agregar mensaje inicial
  } = useRealTimeConversation(generatedCharacter, isGameStarted);

  // 👈 Efecto para agregar mensaje inicial cuando llegue
  useEffect(() => {
    if (initialMessage && isGameStarted) {
      console.log('📝 Agregando mensaje inicial al historial:', initialMessage);
      addInitialMessage(initialMessage);
    }
  }, [initialMessage, isGameStarted, addInitialMessage]);

  // Auto-activar cuando el juego inicia
  useEffect(() => {
    if (isGameStarted && !isActive && isSupported) {
      const timer = setTimeout(async () => {
        try {
          const success = await startConversation();
          if (success) {
            enableSmartMicrophone();
            console.log('✅ Chat de voz auto-activado');
          }
        } catch (error) {
          console.error('❌ Error auto-activando chat:', error);
        }
      }, 800);

      return () => clearTimeout(timer);
    }
  }, [isGameStarted, isActive, isSupported, startConversation, enableSmartMicrophone]);

  const handleButtonClick = () => {
    if (isActive) {
      stopConversation();
    } else {
      startConversation().then(success => {
        if (success) {
          enableSmartMicrophone();
        }
      });
    }
  };

  if (!isGameStarted) {
    return null;
  }

  if (!isSupported) {
    return (
      <div style={{
        backgroundColor: '#fff3cd',
        border: '2px solid #ffc107',
        borderRadius: '12px',
        padding: '20px',
        marginTop: '20px',
        textAlign: 'center'
      }}>
        <h4 style={{ color: '#856404', marginBottom: '8px' }}>
          ⚠️ Reconocimiento de voz no disponible
        </h4>
        <p style={{ color: '#856404', margin: 0 }}>
          Tu navegador no soporta esta funcionalidad
        </p>
      </div>
    );
  }

  return (
    <>
      <style>{`
        @keyframes slideIn {
          from {
            opacity: 0;
            transform: translateY(10px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }
      `}</style>

      <div style={{
        backgroundColor: '#ffffff',
        border: '2px solid #e9ecef',
        borderRadius: '16px',
        padding: '24px',
        marginTop: '20px',
        boxShadow: '0 4px 20px rgba(0,0,0,0.08)'
      }}>

        {/* Botón de estado animado */}
        <div style={{
          display: 'flex',
          justifyContent: 'center',
          marginBottom: '24px'
        }}>
          <AnimatedVoiceButton
            state={conversationState}
            isActive={isActive}
            onClick={handleButtonClick}
          />
        </div>

        {/* Error si existe */}
        {error && (
          <div style={{
            backgroundColor: '#f8d7da',
            border: '2px solid #dc3545',
            borderRadius: '8px',
            padding: '12px',
            marginBottom: '20px',
            color: '#721c24',
            fontSize: '14px',
            textAlign: 'center'
          }}>
            ❌ {error}
          </div>
        )}

        {/* Área de chat histórico */}
        <div style={{
          backgroundColor: '#f8f9fa',
          border: '1px solid #e9ecef',
          borderRadius: '12px',
          padding: '20px',
          minHeight: '300px',
          maxHeight: '450px',
          overflowY: 'auto',
          scrollBehavior: 'smooth'
        }}>
          {messages.length === 0 ? (
            <div style={{
              textAlign: 'center',
              color: '#6c757d',
              fontSize: '16px',
              paddingTop: '60px'
            }}>
              <div style={{ fontSize: '48px', marginBottom: '16px' }}>💬</div>
              <p style={{ margin: '8px 0' }}>
                {isActive
                  ? '¡Conversación activa! Puedes empezar a hablar'
                  : 'Inicia la conversación para comenzar a chatear'
                }
              </p>
              <p style={{
                fontSize: '13px',
                opacity: 0.8,
                marginTop: '12px'
              }}>
                El micrófono se gestiona automáticamente
              </p>
            </div>
          ) : (
            <>
              {messages.map((message) => (
                <MessageBubble key={message.id} message={message} />
              ))}
              {/* Auto scroll al último mensaje */}
              <div style={{ height: '1px' }} ref={(el) => {
                if (el && messages.length > 0) {
                  el.scrollIntoView({ behavior: 'smooth' });
                }
              }} />
            </>
          )}
        </div>

        {/* Indicador de estado minimalista */}
        <div style={{
          textAlign: 'center',
          marginTop: '16px',
          fontSize: '12px',
          color: '#6c757d'
        }}>
          💡 Habla cuando veas el micrófono pulsando • El sistema gestiona todo automáticamente
        </div>
      </div>
    </>
  );
};
