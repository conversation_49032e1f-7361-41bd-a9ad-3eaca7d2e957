import type {
  ISpeechRecognitionService,
  ConversationState,
  SpeechRecognitionResult,
  ConversationStateChange,
} from "./impl/ISpeechRecognitionService";

// Extender la interfaz Window para incluir webkitSpeechRecognition
declare global {
  interface Window {
    webkitSpeechRecognition: any;
    SpeechRecognition: any;
  }
}

export class SpeechRecognitionService implements ISpeechRecognitionService {
  private static instance: SpeechRecognitionService;
  private recognition: any = null;
  private conversationState: ConversationState = "idle";
  private isCurrentlyListening = false;
  private smartMicrophoneEnabled = true;

  // Callbacks
  private resultCallback?: (result: SpeechRecognitionResult) => void;
  private stateChangeCallback?: (change: ConversationStateChange) => void;
  private errorCallback?: (error: string) => void;

  // Configuración
  private language = "es-ES";
  private continuous = true;
  private interimResults = true;

  private constructor() {
    this.initializeRecognition();
  }

  static getInstance(): SpeechRecognitionService {
    if (!SpeechRecognitionService.instance) {
      SpeechRecognitionService.instance = new SpeechRecognitionService();
    }
    return SpeechRecognitionService.instance;
  }

  private initializeRecognition(): void {
    if (!this.isSupported()) {
      console.warn("⚠️ Speech Recognition no está soportado en este navegador");
      return;
    }

    const SpeechRecognition =
      window.SpeechRecognition || window.webkitSpeechRecognition;
    this.recognition = new SpeechRecognition();

    this.recognition.lang = this.language;
    this.recognition.continuous = this.continuous;
    this.recognition.interimResults = this.interimResults;
    this.recognition.maxAlternatives = 1;

    this.setupEventListeners();
  }

  private setupEventListeners(): void {
    if (!this.recognition) return;

    this.recognition.onstart = () => {
      console.log("🎤 Reconocimiento de voz iniciado");
      this.isCurrentlyListening = true;
      this.setConversationState("listening");
    };

    this.recognition.onend = () => {
      console.log("🎤 Reconocimiento de voz finalizado");
      this.isCurrentlyListening = false;
      if (this.conversationState === "listening") {
        this.setConversationState("idle");
      }
    };

    this.recognition.onresult = (event: any) => {
      const result = event.results[event.results.length - 1];
      const transcript = result[0].transcript;
      const confidence = result[0].confidence;
      const isFinal = result.isFinal;

      console.log(
        `🎤 Resultado: "${transcript}" (confianza: ${confidence}, final: ${isFinal})`
      );

      if (this.resultCallback) {
        this.resultCallback({
          transcript,
          confidence,
          isFinal,
        });
      }
    };

    this.recognition.onerror = (event: any) => {
      console.error("❌ Error en reconocimiento de voz:", event.error);
      this.isCurrentlyListening = false;

      let errorMessage = "Error desconocido en el reconocimiento de voz";
      switch (event.error) {
        case "no-speech":
          errorMessage = "No se detectó habla";
          break;
        case "audio-capture":
          errorMessage = "No se pudo acceder al micrófono";
          break;
        case "not-allowed":
          errorMessage = "Permisos de micrófono denegados";
          break;
        case "network":
          errorMessage = "Error de red";
          break;
        default:
          errorMessage = `Error: ${event.error}`;
      }

      if (this.errorCallback) {
        this.errorCallback(errorMessage);
      }
    };
  }

  async startListening(): Promise<boolean> {
    if (!this.isSupported()) {
      console.error("❌ Speech Recognition no está soportado");
      return false;
    }

    if (this.isCurrentlyListening) {
      console.log("🎤 Ya está escuchando");
      return true;
    }

    // Verificar si estamos en estado de habla (para evitar bucles)
    if (this.smartMicrophoneEnabled && this.conversationState === "speaking") {
      console.log("🔇 No se inicia el micrófono porque la IA está hablando");
      return false;
    }

    try {
      this.recognition.start();
      return true;
    } catch (error) {
      console.error("❌ Error iniciando reconocimiento:", error);
      if (this.errorCallback) {
        this.errorCallback("No se pudo iniciar el reconocimiento de voz");
      }
      return false;
    }
  }

  stopListening(): void {
    if (this.recognition && this.isCurrentlyListening) {
      this.recognition.stop();
    }
  }

  isListening(): boolean {
    return this.isCurrentlyListening;
  }

  getConversationState(): ConversationState {
    return this.conversationState;
  }

  setConversationState(state: ConversationState): void {
    const previousState = this.conversationState;
    this.conversationState = state;

    console.log(`🔄 Estado de conversación: ${previousState} → ${state}`);

    // Control inteligente del micrófono
    if (this.smartMicrophoneEnabled) {
      this.handleSmartMicrophoneControl(state, previousState);
    }

    if (this.stateChangeCallback) {
      this.stateChangeCallback({ state });
    }
  }

  private handleSmartMicrophoneControl(
    newState: ConversationState,
    previousState: ConversationState
  ): void {
    switch (newState) {
      case "speaking":
        // La IA está hablando, detener el micrófono
        if (this.isCurrentlyListening) {
          console.log("🔇 Deteniendo micrófono porque la IA está hablando");
          this.stopListening();
        }
        break;

      case "idle":
        // La IA terminó de hablar, reactivar el micrófono después de un breve delay
        if (previousState === "speaking") {
          setTimeout(() => {
            if (this.conversationState === "idle") {
              console.log(
                "🎤 Reactivando micrófono después de que la IA terminó de hablar"
              );
              this.startListening();
            }
          }, 1000); // Esperar 1 segundo para evitar capturar el final del audio
        }
        break;

      case "processing":
        // Procesando respuesta, mantener micrófono apagado
        if (this.isCurrentlyListening) {
          this.stopListening();
        }
        break;
    }
  }

  onResult(callback: (result: SpeechRecognitionResult) => void): void {
    this.resultCallback = callback;
  }

  onStateChange(callback: (change: ConversationStateChange) => void): void {
    this.stateChangeCallback = callback;
  }

  onError(callback: (error: string) => void): void {
    this.errorCallback = callback;
  }

  enableSmartMicrophoneControl(): void {
    this.smartMicrophoneEnabled = true;
    console.log("✅ Control inteligente de micrófono activado");
  }

  disableSmartMicrophoneControl(): void {
    this.smartMicrophoneEnabled = false;
    console.log("❌ Control inteligente de micrófono desactivado");
  }

  setLanguage(language: string): void {
    this.language = language;
    if (this.recognition) {
      this.recognition.lang = language;
    }
  }

  setContinuous(continuous: boolean): void {
    this.continuous = continuous;
    if (this.recognition) {
      this.recognition.continuous = continuous;
    }
  }

  setInterimResults(interimResults: boolean): void {
    this.interimResults = interimResults;
    if (this.recognition) {
      this.recognition.interimResults = interimResults;
    }
  }

  isSupported(): boolean {
    return !!(window.SpeechRecognition || window.webkitSpeechRecognition);
  }
}
